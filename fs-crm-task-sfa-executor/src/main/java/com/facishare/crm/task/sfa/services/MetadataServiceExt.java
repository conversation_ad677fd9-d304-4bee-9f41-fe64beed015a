package com.facishare.crm.task.sfa.services;

import com.facishare.crm.task.sfa.common.exception.TerminationException;
import com.facishare.crm.task.sfa.services.model.PrmModel;
import com.facishare.crm.task.sfa.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.common.util.BulkOpResult;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/17 12:29
 */
@Service
@Slf4j
public class MetadataServiceExt {
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    DescribeLogicServiceImpl describeLogicService;
    @Autowired
    private ServiceFacade serviceFacade;

    public IObjectData findObjectByField(User user, String objectApiName, String field, String value) {
        if (StringUtils.isBlank(field) || StringUtils.isBlank(value)) {
            return null;
        }
        int limit = 1;
        SearchTemplateQuery query = buildSearchQueryByField(field, Lists.newArrayList(value), limit);
        if (query == null) {
            return null;
        }
        QueryResult<IObjectData> searchData = metaDataService.findBySearchQuery(user, objectApiName, query);
        List<IObjectData> dataList = searchData.getData();
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0);
        }
        return null;
    }

    /**
     * 限制数据 十万
     */
    public List<IObjectData> findObjectsByField(User user, String objectApiName, String field, List<String> values) {
        if (StringUtils.isBlank(field) || CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }
        int maxCount = 100000;
        // 每次查询个数
        int limit = 1000;
        // 当前循环次数
        int loop = 0;
        SearchTemplateQuery query = buildSearchQueryByField(field, values, limit);
        if (query == null) {
            return Lists.newArrayList();
        }
        List<IObjectData> result = Lists.newArrayList();
        List<IObjectData> currentDataList;
        query.setNeedReturnCountNum(false);
        query.setFindExplicitTotalNum(false);
        query.setNeedReturnQuote(false);
        query.setPermissionType(0);
        query.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, true)));

        IActionContext actionContext = ActionContextExt.of(user)
                .disableDeepQuote()
                .getContext();
        actionContext.setDoCalculate(false);
        do {
            query.setOffset(loop++ * limit);
            QueryResult<IObjectData> searchData = metaDataService.findBySearchQuery(user, objectApiName, query);
            result.addAll(searchData.getData());
            currentDataList = searchData.getData();
        } while (currentDataList != null && currentDataList.size() >= limit && result.size() <= maxCount && loop <= 100);
        if (result.size() > maxCount || loop > 100) {
            log.error("data size > 100000, tenant:{}, obj:{}, ids:{}", user.getTenantId(), objectApiName, values);
        }
        return result;
    }

    public List<IObjectData> findObjectsIgnoreAll(User user, String objectApiName) {
        if (StringUtils.isBlank(objectApiName)) {
            return Lists.newArrayList();
        }
        int maxCount = 100000;
        // 每次查询个数
        int limit = 1000;
        // 当前循环次数
        int loop = 0;
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(limit);
        List<IObjectData> result = Lists.newArrayList();
        List<IObjectData> currentDataList;

        query.setNeedReturnCountNum(false);
        query.setFindExplicitTotalNum(false);
        query.setNeedReturnQuote(false);
        query.setPermissionType(0);
        query.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, true)));
        IActionContext actionContext = ActionContextExt.of(user)
                .disableDeepQuote()
                .getContext();
        actionContext.setDoCalculate(false);
        do {
            query.setOffset(loop++ * limit);
            QueryResult<IObjectData> searchData = metaDataService.findBySearchQueryIgnoreAll(user, objectApiName, query);
            result.addAll(searchData.getData());
            currentDataList = searchData.getData();
        } while (currentDataList != null && currentDataList.size() >= limit && result.size() <= maxCount && loop <= 100);
        if (result.size() > maxCount || loop > 100) {
            log.error("data size > 100000, tenant:{}, obj:{}", user.getTenantId(), objectApiName);
        }
        return result;
    }

    public void findObjectsByField(User user, String objectApiName, String field, List<String> values, ProcessInSearch invoke) {
        if (StringUtils.isBlank(field) || CollectionUtils.isEmpty(values)) {
            return;
        }
        if ("_id".equals(field) && values.size() <= 100) {
            List<IObjectData> dealDataList = Lists.newArrayList();
            if (values.size() == 1) {
                IObjectData objectDataIgnoreFormula = metaDataService.findObjectDataIgnoreFormula(user, values.get(0), objectApiName);
                dealDataList.add(objectDataIgnoreFormula);
            } else {
                List<IObjectData> objectDataByIds = metaDataService.findObjectDataByIdsIgnoreFormula(user.getTenantId(), values, objectApiName);
                dealDataList.addAll(objectDataByIds);
            }
            try {
                invoke.process(dealDataList);
            } catch (Exception e) {
                log.error("deal data failed, objectApiName:{}， tenant:{}", objectApiName, user.getTenantId(), e);
            }
            return;
        }
        // 每次查询个数
        int limit = 200;
        // 当前循环次数
        int loop = 0;
        SearchTemplateQuery query = buildSearchQueryByField(field, values, limit);
        if (query == null) {
            return;
        }

        query.setNeedReturnCountNum(false);
        query.setFindExplicitTotalNum(false);
        query.setNeedReturnQuote(false);
        query.setPermissionType(0);
        query.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, true)));
        IActionContext actionContext = ActionContextExt.of(user)
                .disableDeepQuote()
                .getContext();
        actionContext.setDoCalculate(false);

        List<IObjectData> currentDataList;
        do {
            query.setOffset(loop++ * limit);
            QueryResult<IObjectData> searchData = metaDataService.findBySearchQuery(actionContext, objectApiName, query);
            try {
                invoke.process(searchData.getData());
            } catch (Exception e) {
                log.error("deal data failed, objectApiName:{}， tenant:{}", objectApiName, user.getTenantId(), e);
            }
            currentDataList = searchData.getData();
        } while (currentDataList != null && currentDataList.size() >= limit);
    }

    private SearchTemplateQuery buildSearchQueryByField(String field, List<String> values, int limit) {
        values.remove("");
        if (StringUtils.isBlank(field) || CollectionUtils.isEmpty(values)) {
            return null;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        IFilter filter = new Filter();
        filter.setFieldName(field);
        if (values.size() == 1) {
            filter.setOperator(Operator.EQ);
        } else {
            filter.setOperator(Operator.IN);
        }
        filter.setFieldValues(values);
        query.setFilters(Lists.newArrayList(filter));
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setLimit(limit);
        return query;
    }

    public void batchUpdateByFields(User user, List<IObjectData> dataList, List<String> updateFields) {
        metaDataService.batchUpdateByFields(user, dataList, updateFields);
    }

    public void batchUpdateByFields(IActionContext context, List<IObjectData> dataList, List<String> updateFields) {
        metaDataService.batchUpdateByFields(context, dataList, updateFields);
    }

    public BulkOpResult batchUpdateRelevantTeam(User user, List<IObjectData> dataList) {
        return metaDataService.batchUpdateRelevantTeam(user, dataList, false);
    }

    public List<IObjectData> findDataListByMatch(User user, String objectApiName, String field, String matchValue) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterMatch(filters, field, matchValue);
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        IObjectDescribe objectDescribe = describeLogicService.findObject(user.getTenantId(), objectApiName);
        QueryResult<IObjectData> queryData = metaDataService.findBySearchQueryWithDeleted(user, objectDescribe, searchTemplateQuery);
        if (CollectionUtils.isEmpty(queryData.getData())) {
            return Lists.newArrayList();
        }
        return queryData.getData();
    }

    private SearchTemplateQuery getSearchTemplateQuery(List<IFilter> filters) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }

    public void bulkUpdateByFields(User user, List<IObjectData> dataList, List<String> updateFieldList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        List<List<IObjectData>> partition = Lists.partition(dataList, 1000);
        for (List<IObjectData> list : partition) {
            metaDataService.batchUpdateByFields(user, list, updateFieldList);
        }
    }

    public List<IObjectData> findBySearchQueryIgnoreAll(User user, String objectApiName, SearchTemplateQuery query) {
        return metaDataService.findBySearchQueryIgnoreAll(user, objectApiName, query).getData();
    }

    public List<IObjectData> findBySearchQueryWithFieldsIgnoreAll(User user, String objectApiName, SearchTemplateQuery query, List<String> fields) {
        if (CollectionUtils.isEmpty(fields)) {
            return findBySearchQueryIgnoreAll(user, objectApiName, query);
        }
        return metaDataService.findBySearchQueryWithFieldsIgnoreAll(user, objectApiName, query, fields).getData();
    }

    public void findBySearchQueryWithFieldsIgnoreAll(User user, String objectApiName, SearchTemplateQuery query, List<String> fields, ProcessInSearch inSearch) {
        if (CollectionUtils.isEmpty(fields)) {
            return;
        }
        // 每次查询个数
        int limit = 1000;
        // 当前循环次数
        int loop = 0;
        query.setLimit(limit);
        query.setOffset(0);
        query.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, true)));
        List<IObjectData> currentDataList;
        do {
            query.setOffset(loop++ * limit);
            QueryResult<IObjectData> searchData = metaDataService.findBySearchQueryWithFieldsIgnoreAll(user, objectApiName, query, fields);
            try {
                inSearch.process(searchData.getData());
            } catch (Exception e) {
                log.error("findBySearchQueryWithFieldsIgnoreAll data failed, objectApiName:{}， tenant:{}", objectApiName, user.getTenantId(), e);
            }
            currentDataList = searchData.getData();
        } while (currentDataList != null && currentDataList.size() >= limit);
    }

    public IObjectData findObjectByIdIgnoreAll(User user, String dataId, String objectApiName) {
        if (StringUtils.isAnyBlank(dataId, objectApiName, user.getTenantId())) {
            return null;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterEq(filters, "_id", dataId);
        List<IObjectData> dataList = findBySearchQueryIgnoreAll(user, objectApiName, query);
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0);
        }
        return null;
    }

    public IObjectData findObjectById(User user, String dataId, String objectApiName) {
        if (StringUtils.isAnyBlank(dataId, objectApiName)) {
            return null;
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setSearchSource("db");
        List<IFilter> filters = query.getFilters();
        SearchUtil.fillFilterEq(filters, "_id", dataId);
        List<IObjectData> dataList = metaDataService.findBySearchQuery(user, objectApiName, query).getData();
        if (CollectionUtils.isNotEmpty(dataList)) {
            return dataList.get(0);
        }
        return null;
    }

    public void findObjectBySearchQueryIgnoreAll(User user, String objectApiName, SearchTemplateQuery query, ProcessInSearch invoke) {
        // 每次查询个数
        int limit = 200;
        // 当前循环次数
        int loop = 0;
        query.setLimit(limit);
        query.setOffset(0);
        query.setPermissionType(0);
        query.setNeedReturnCountNum(false);
        query.setFindExplicitTotalNum(false);
        query.setNeedReturnQuote(false);
        query.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, true)));
        List<IObjectData> currentDataList;
        do {
            query.setOffset(loop++ * limit);
            QueryResult<IObjectData> searchData = metaDataService.findBySearchQueryIgnoreAll(user, objectApiName, query);
            try {
                invoke.process(searchData.getData());
            } catch (Exception e) {
                log.error("findObjectBySearchQueryIgnoreAll data failed, objectApiName:{}， tenant:{}", objectApiName, user.getTenantId(), e);
            }
            currentDataList = searchData.getData();
        } while (currentDataList != null && currentDataList.size() >= limit);
    }

    public void dealObjectDataBySearchQuery(User user, String objectApiName, SearchTemplateQuery query, ProcessInSearchWithReturn invoke) {
        // 每次查询个数
        int limit;
        if (query.getLimit() <= 0 || query.getLimit() > 1000) {
            limit = 200;
        } else {
            limit = query.getLimit();
        }
        // 当前循环次数
        query.setLimit(limit);
        query.setOffset(0);
        query.setNeedReturnCountNum(false);
        query.setFindExplicitTotalNum(false);
        query.setNeedReturnQuote(false);
        query.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, true)));
        IActionContext actionContext = ActionContextExt.of(user)
                .disableDeepQuote()
                .getContext();
        actionContext.setDoCalculate(false);
        List<IObjectData> currentDataList;
        int count = 1;
        Set<String> excludeIds = Sets.newHashSet();
        do {
            if (CollectionUtils.isNotEmpty(excludeIds)) {
                SearchUtil.fillFilterNotIN(query.getFilters(), ObjectDataExt.ID, excludeIds);
            }
            QueryResult<IObjectData> searchData = metaDataService.findBySearchQuery(actionContext, objectApiName, query);
            try {
                log.info("dealObjectDataBySearchQuery tenant:{}, objectApiName:{}, count:{}", user.getTenantId(), objectApiName, count++);
                Set<String> failedIds = invoke.process(searchData.getData());
                if (CollectionUtils.isNotEmpty(failedIds)) {
                    excludeIds.addAll(failedIds);
                }
            } catch (TerminationException te) {
                log.warn("TerminationException, objectApiName:{}， tenant:{}", objectApiName, user.getTenantId(), te);
                break;
            } catch (Exception e) {
                log.error("deal data failed, objectApiName:{}， tenant:{}", objectApiName, user.getTenantId(), e);
            }
            // 1000*200 = 200000
            if (count >= 1000) {
                log.error("deal data count:{}, objectApiName:{}， tenant:{}", count, objectApiName, user.getTenantId());
                break;
            }
            currentDataList = searchData.getData();
        } while (currentDataList != null && currentDataList.size() == limit);
    }

    @Transactional
    public void updatePartnerAndOutOwner(User user, @NotNull IObjectData updatedData, @NotNull PrmModel.UpdateDataPartnerAndOutOwnerArg arg) {
        List<String> updateFields = Lists.newArrayList(ObjectDataExt.OUTER_OWNER,
                ObjectDataExt.OUTER_TENANT);
        if ("PartnerObj".equals(arg.getObjectApiName())) {
            updateFields.add(ObjectDataExt.PARTNER_ID);
            updatedData.set(ObjectDataExt.PARTNER_ID, arg.getDataId());
        }
        updatedData.setOutTenantId(arg.getOuterTenantId());
        updatedData.setOutOwner(Lists.newArrayList(arg.getOutOwnerId()));
        bulkUpdateByFields(user, Lists.newArrayList(updatedData), updateFields);
        log.info("更新外部负责人：已更新。tenant:{}, updatedData:{}, updatedData:{}, arg:{}", user.getTenantId(), updatedData.getId(), updatedData.getName(), arg);
        addMember2RelevantTeam(user, updatedData, arg.getOuterTenantId(), arg.getOutOwnerId());
    }

    public void addMember2RelevantTeam(User user, @NotNull IObjectData updatedData, String outerTenantId, String outOwnerId) {
        List<String> originalMembers = addMember2RelevantTeam(updatedData, outerTenantId, outOwnerId);
        BulkOpResult bulkOpResult = batchUpdateRelevantTeam(user, Lists.newArrayList(updatedData));
        try {
            addUpdateRelevantTeamLog(user, updatedData, bulkOpResult, originalMembers);
        } catch (Exception e) {
            log.warn("更新外部负责人：记录日志错误。tenant:{}", user.getTenantId(), e);
        }
    }

    private void addUpdateRelevantTeamLog(User user, @NotNull IObjectData updatedData, BulkOpResult bulkOpResult, List<String> originalMembers) {
        if (bulkOpResult == null || updatedData == null) {
            log.info("更新相关团队：已更新。bulkOpResult 是空的！tenant:{}", user.getTenantId());
            return;
        }
        Set<String> successIdSet = Optional.ofNullable(bulkOpResult.getSuccessObjectDataList())
                .orElse(Lists.newArrayList())
                .stream()
                .map(DBRecord::getId)
                .collect(Collectors.toSet());

        Set<String> failedIdSet = Optional.ofNullable(bulkOpResult.getFailObjectDataList())
                .orElse(Lists.newArrayList())
                .stream()
                .map(DBRecord::getId)
                .collect(Collectors.toSet());
        ObjectDataExt objectDataExt = ObjectDataExt.of(updatedData);
        List<String> employeeList = objectDataExt.getTeamMembers().stream().map(TeamMember::getEmployee).collect(Collectors.toList());
        log.info("更新相关团队：记录更新日志。tenant:{}, bulkOpResult->success:{}, bulkOpResult->failed:{}, failedReason:{}, originalMembers:{} ->employeeList:{}", user.getTenantId(),
                successIdSet, failedIdSet, bulkOpResult.getFailReason(), originalMembers, employeeList);
    }

    private List<String> addMember2RelevantTeam(@NotNull IObjectData objectData, @NotNull String outerTenantId, @NotNull String outOwnerId) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        List<String> originalMembers = teamMembers.stream().map(TeamMember::getEmployee).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        teamMembers.removeIf(t -> (t.getRole() == TeamMember.Role.OWNER && t.isOutMember()));
        teamMembers.add(new TeamMember(outOwnerId, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, outerTenantId));
        objectDataExt.setTeamMembers(teamMembers);
        return originalMembers;
    }

    public void bulkCreate(User user, List<IObjectData> createDataList) {
        if (CollectionUtils.isEmpty(createDataList)) {
            return;
        }
        serviceFacade.bulkSaveObjectData(createDataList, user);
    }
}
